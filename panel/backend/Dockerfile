##### Frontend build stage
FROM node:18-alpine AS fe
WORKDIR /fe

# Copy and build frontend
COPY frontend ./frontend
WORKDIR /fe/frontend
# Use same-origin API during build
ENV REACT_APP_API_URL=/api/v1
RUN npm ci && npm run build

##### Go build stage
FROM golang:1.24-alpine AS builder
WORKDIR /app

# Install CA certs and git (for go modules)
RUN apk add --no-cache ca-certificates git

# Copy go mod/sum and download deps
COPY backend/go.mod backend/go.sum ./
RUN go mod download

# Copy backend source
COPY backend .

# Copy built frontend into dist before compiling
RUN rm -rf ./dist && mkdir -p ./dist
COPY --from=fe /fe/frontend/build ./dist

# Build
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

##### Runtime image
FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main ./main
COPY --from=builder /app/config.yaml ./config.yaml

EXPOSE 8000
CMD ["./main"]
