# Changes to PostCSS Environment Variables

### 4.0.6 (March 19, 2022)

- Add deprecation notice for `postcss-env-function`

[see the discussion](https://github.com/csstools/postcss-plugins/discussions/192)

### 4.0.5 (February 5, 2022)

- Improved `es module` and `commonjs` compatibility

### 4.0.4 (January 2, 2022)

- Removed Sourcemaps from package tarball.
- Moved CLI to CLI Package. See [announcement](https://github.com/csstools/postcss-plugins/discussions/121).

### 4.0.3 (December 13, 2021)

- Changed: now uses `postcss-value-parser` for parsing.
- Updated: documentation

### 4.0.2 (November 18, 2021)

- Added: Safeguards against postcss-values-parser potentially throwing an error.

### 4.0.1 (October 28, 2021)

- Updated: Enforcing styling consistency
- Updated: `postcss-values-parser` to 6.0.1 (patch).

### 4.0.0 (September 17, 2021)

- Updated: Support for PostCS 8+ (major).
- Updated: Support for Node 12+ (major).

### 3.0.0 (June 13, 2019)

- Updated: `postcss-values-parser` to 3.2.0 (major)
- Updated: `postcss` to 7.0.27 (patch)
- Updated: Support for Node 10+ (major)

### 2.0.2 (September 20, 2018)

- Updated: Do not break on an empty importFrom object

### 2.0.1 (September 18, 2018)

- Updated: Support for PostCSS Values Parser 2

### 2.0.0 (September 17, 2018)

- Updated: Support for PostCSS 7+
- Updated: Support for Node 6+
- Updated: Changed `variables` option to `importFrom` option

### 1.0.0 (April 28, 2018)

- Initial version
