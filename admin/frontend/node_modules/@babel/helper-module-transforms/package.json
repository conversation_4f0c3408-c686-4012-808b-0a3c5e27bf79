{"name": "@babel/helper-module-transforms", "version": "7.28.3", "description": "Babel helper functions for implementing ES6 module transformations", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-transforms"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.28.3"}, "devDependencies": {"@babel/core": "^7.28.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}