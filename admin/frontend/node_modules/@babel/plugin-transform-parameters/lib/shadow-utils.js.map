{"version": 3, "names": ["_core", "require", "iifeVisitor", "exports", "ReferencedIdentifier|BindingIdentifier", "path", "state", "scope", "node", "name", "getBinding", "parent", "hasOwnBinding", "needsOuterBinding", "stop", "skip", "collectShadowedParamsNames", "param", "functionScope", "shadowedP<PERSON><PERSON>", "Object", "keys", "getBindingIdentifiers", "_functionScope$bindin", "constantViolations", "bindings", "redeclarator", "type", "init", "declaration", "parentPath", "isFor", "get", "remove", "add", "buildScopeIIFE", "body", "args", "params", "push", "t", "identifier", "returnStatement", "callExpression", "arrowFunctionExpression"], "sources": ["../src/shadow-utils.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { Node<PERSON><PERSON>, Scope, Visitor } from \"@babel/core\";\n\ntype State = {\n  needsOuterBinding: boolean;\n  scope: Scope;\n};\n\nexport const iifeVisitor: Visitor<State> = {\n  \"ReferencedIdentifier|BindingIdentifier\"(\n    path: NodePath<t.Identifier>,\n    state,\n  ) {\n    const { scope, node } = path;\n    const { name } = node;\n\n    if (\n      name === \"eval\" ||\n      (scope.getBinding(name) === state.scope.parent.getBinding(name) &&\n        state.scope.hasOwnBinding(name))\n    ) {\n      state.needsOuterBinding = true;\n      path.stop();\n    }\n  },\n  // type annotations don't use or introduce \"real\" bindings\n  \"TypeAnnotation|TSTypeAnnotation|TypeParameterDeclaration|TSTypeParameterDeclaration\":\n    (path: NodePath) => path.skip(),\n};\n\nexport function collectShadowedParamsNames(\n  param: NodePath<t.Function[\"params\"][number]>,\n  functionScope: Scope,\n  shadowedParams: Set<string>,\n) {\n  for (const name of Object.keys(param.getBindingIdentifiers())) {\n    const constantViolations = functionScope.bindings[name]?.constantViolations;\n    if (constantViolations) {\n      for (const redeclarator of constantViolations) {\n        const node = redeclarator.node;\n        // If a constant violation is a var or a function declaration,\n        // we first check to see if it's a var without an init.\n        // If so, we remove that declarator.\n        // Otherwise, we have to wrap it in an IIFE.\n        switch (node.type) {\n          case \"VariableDeclarator\": {\n            if (node.init === null) {\n              const declaration = redeclarator.parentPath;\n              // The following uninitialized var declarators should not be removed\n              // for (var x in {})\n              // for (var x;;)\n              if (\n                !declaration.parentPath.isFor() ||\n                declaration.parentPath.get(\"body\") === declaration\n              ) {\n                redeclarator.remove();\n                break;\n              }\n            }\n\n            shadowedParams.add(name);\n            break;\n          }\n          case \"FunctionDeclaration\":\n            shadowedParams.add(name);\n            break;\n        }\n      }\n    }\n  }\n}\n\nexport function buildScopeIIFE(\n  shadowedParams: Set<string>,\n  body: t.BlockStatement,\n) {\n  const args = [];\n  const params = [];\n\n  for (const name of shadowedParams) {\n    // We create them twice; the other option is to use t.cloneNode\n    args.push(t.identifier(name));\n    params.push(t.identifier(name));\n  }\n\n  return t.returnStatement(\n    t.callExpression(t.arrowFunctionExpression(params, body), args),\n  );\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAQO,MAAMC,WAA2B,GAAAC,OAAA,CAAAD,WAAA,GAAG;EACzC,wCAAwCE,CACtCC,IAA4B,EAC5BC,KAAK,EACL;IACA,MAAM;MAAEC,KAAK;MAAEC;IAAK,CAAC,GAAGH,IAAI;IAC5B,MAAM;MAAEI;IAAK,CAAC,GAAGD,IAAI;IAErB,IACEC,IAAI,KAAK,MAAM,IACdF,KAAK,CAACG,UAAU,CAACD,IAAI,CAAC,KAAKH,KAAK,CAACC,KAAK,CAACI,MAAM,CAACD,UAAU,CAACD,IAAI,CAAC,IAC7DH,KAAK,CAACC,KAAK,CAACK,aAAa,CAACH,IAAI,CAAE,EAClC;MACAH,KAAK,CAACO,iBAAiB,GAAG,IAAI;MAC9BR,IAAI,CAACS,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAED,qFAAqF,EAClFT,IAAc,IAAKA,IAAI,CAACU,IAAI,CAAC;AAClC,CAAC;AAEM,SAASC,0BAA0BA,CACxCC,KAA6C,EAC7CC,aAAoB,EACpBC,cAA2B,EAC3B;EACA,KAAK,MAAMV,IAAI,IAAIW,MAAM,CAACC,IAAI,CAACJ,KAAK,CAACK,qBAAqB,CAAC,CAAC,CAAC,EAAE;IAAA,IAAAC,qBAAA;IAC7D,MAAMC,kBAAkB,IAAAD,qBAAA,GAAGL,aAAa,CAACO,QAAQ,CAAChB,IAAI,CAAC,qBAA5Bc,qBAAA,CAA8BC,kBAAkB;IAC3E,IAAIA,kBAAkB,EAAE;MACtB,KAAK,MAAME,YAAY,IAAIF,kBAAkB,EAAE;QAC7C,MAAMhB,IAAI,GAAGkB,YAAY,CAAClB,IAAI;QAK9B,QAAQA,IAAI,CAACmB,IAAI;UACf,KAAK,oBAAoB;YAAE;cACzB,IAAInB,IAAI,CAACoB,IAAI,KAAK,IAAI,EAAE;gBACtB,MAAMC,WAAW,GAAGH,YAAY,CAACI,UAAU;gBAI3C,IACE,CAACD,WAAW,CAACC,UAAU,CAACC,KAAK,CAAC,CAAC,IAC/BF,WAAW,CAACC,UAAU,CAACE,GAAG,CAAC,MAAM,CAAC,KAAKH,WAAW,EAClD;kBACAH,YAAY,CAACO,MAAM,CAAC,CAAC;kBACrB;gBACF;cACF;cAEAd,cAAc,CAACe,GAAG,CAACzB,IAAI,CAAC;cACxB;YACF;UACA,KAAK,qBAAqB;YACxBU,cAAc,CAACe,GAAG,CAACzB,IAAI,CAAC;YACxB;QACJ;MACF;IACF;EACF;AACF;AAEO,SAAS0B,cAAcA,CAC5BhB,cAA2B,EAC3BiB,IAAsB,EACtB;EACA,MAAMC,IAAI,GAAG,EAAE;EACf,MAAMC,MAAM,GAAG,EAAE;EAEjB,KAAK,MAAM7B,IAAI,IAAIU,cAAc,EAAE;IAEjCkB,IAAI,CAACE,IAAI,CAACC,WAAC,CAACC,UAAU,CAAChC,IAAI,CAAC,CAAC;IAC7B6B,MAAM,CAACC,IAAI,CAACC,WAAC,CAACC,UAAU,CAAChC,IAAI,CAAC,CAAC;EACjC;EAEA,OAAO+B,WAAC,CAACE,eAAe,CACtBF,WAAC,CAACG,cAAc,CAACH,WAAC,CAACI,uBAAuB,CAACN,MAAM,EAAEF,IAAI,CAAC,EAAEC,IAAI,CAChE,CAAC;AACH", "ignoreList": []}