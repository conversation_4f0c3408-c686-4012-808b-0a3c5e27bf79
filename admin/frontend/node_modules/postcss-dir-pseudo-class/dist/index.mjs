import e from"postcss-selector-parser";function t(t){const r=Object(t).dir,o=Boolean(Object(t).preserve),s=Boolean(Object(t).shadow);return{postcssPlugin:"postcss-dir-pseudo-class",Rule(t,{result:a}){let n,l=!1;if(t.selector.toLowerCase().includes(":dir(")){try{n=e((o=>{o.nodes.forEach((o=>{o.walk((o=>{if("pseudo"!==o.type)return;if(":dir"!==o.value.toLowerCase())return;if(!o.nodes||!o.nodes.length)return;const n=o.nodes.toString().toLowerCase();if("rtl"!==n&&"ltr"!==n)return;const c=o.parent;c.nodes.filter((e=>"pseudo"===e.type&&":dir"===e.value.toLowerCase())).length>1&&!l&&(l=!0,t.warn(a,`Hierarchical :dir pseudo class usage can't be transformed correctly to [dir] attributes. This will lead to incorrect selectors for "${t.selector}"`));const p=o.prev(),i=o.next(),d=p&&p.type&&"combinator"!==p.type,u=i&&i.type&&"combinator"!==i.type,v=i&&i.type&&("combinator"!==i.type||"combinator"===i.type&&" "===i.value);d||u||0===c.nodes.indexOf(o)&&v||1===c.nodes.length?o.remove():o.replaceWith(e.universal());const f=c.nodes[0],b=f&&"combinator"===f.type&&" "===f.value,y=f&&"tag"===f.type&&"html"===f.value.toLowerCase(),m=f&&"pseudo"===f.type&&":root"===f.value.toLowerCase();!f||y||m||b||c.prepend(e.combinator({value:" "}));const h=r===n,w=e.attribute({attribute:"dir",operator:"=",quoteMark:'"',value:`"${n}"`}),g=e.pseudo({value:":host-context"});g.append(w);const C=e.pseudo({value:(y||m?"":"html")+":not"});C.append(e.attribute({attribute:"dir",operator:"=",quoteMark:'"',value:`"${"ltr"===n?"rtl":"ltr"}"`})),h?y?c.insertAfter(f,C):c.prepend(C):y?c.insertAfter(f,w):s&&!m?c.prepend(g):c.prepend(w)}))}))})).processSync(t.selector)}catch(e){return void t.warn(a,`Failed to parse selector : ${t.selector}`)}void 0!==n&&n!==t.selector&&(t.cloneBefore({selector:n}),o||t.remove())}}}}t.postcss=!0;export{t as default};
