{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/nocytech/kpss-plus/admin/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport o, { createContext as H, useContext as E, useEffect as m, useState as u } from \"react\";\nimport { createPortal as g } from \"react-dom\";\nimport { useDisposables as h } from '../hooks/use-disposables.js';\nimport { objectToFormEntries as x } from '../utils/form.js';\nimport { compact as y } from '../utils/render.js';\nimport { Hidden as l, HiddenFeatures as d } from './hidden.js';\nlet f = H(null);\nfunction W(t) {\n  let [e, r] = u(null);\n  return o.createElement(f.Provider, {\n    value: {\n      target: e\n    }\n  }, t.children, o.createElement(l, {\n    features: d.Hidden,\n    ref: r\n  }));\n}\nfunction c(_ref) {\n  let {\n    children: t\n  } = _ref;\n  let e = E(f);\n  if (!e) return o.createElement(o.Fragment, null, t);\n  let {\n    target: r\n  } = e;\n  return r ? g(o.createElement(o.Fragment, null, t), r) : null;\n}\nfunction j(_ref2) {\n  let {\n    data: t,\n    form: e,\n    disabled: r,\n    onReset: n,\n    overrides: F\n  } = _ref2;\n  let [i, a] = u(null),\n    p = h();\n  return m(() => {\n    if (n && i) return p.addEventListener(i, \"reset\", n);\n  }, [i, e, n]), o.createElement(c, null, o.createElement(C, {\n    setForm: a,\n    formId: e\n  }), x(t).map(_ref3 => {\n    let [s, v] = _ref3;\n    return o.createElement(l, _objectSpread({\n      features: d.Hidden\n    }, y(_objectSpread({\n      key: s,\n      as: \"input\",\n      type: \"hidden\",\n      hidden: !0,\n      readOnly: !0,\n      form: e,\n      disabled: r,\n      name: s,\n      value: v\n    }, F))));\n  }));\n}\nfunction C(_ref4) {\n  let {\n    setForm: t,\n    formId: e\n  } = _ref4;\n  return m(() => {\n    if (e) {\n      let r = document.getElementById(e);\n      r && t(r);\n    }\n  }, [t, e]), e ? null : o.createElement(l, {\n    features: d.Hidden,\n    as: \"input\",\n    type: \"hidden\",\n    hidden: !0,\n    readOnly: !0,\n    ref: r => {\n      if (!r) return;\n      let n = r.closest(\"form\");\n      n && t(n);\n    }\n  });\n}\nexport { j as FormFields, W as FormFieldsProvider, c as HoistFormFields };", "map": {"version": 3, "names": ["o", "createContext", "H", "useContext", "E", "useEffect", "m", "useState", "u", "createPortal", "g", "useDisposables", "h", "objectToFormEntries", "x", "compact", "y", "Hidden", "l", "HiddenFeatures", "d", "f", "W", "t", "e", "r", "createElement", "Provider", "value", "target", "children", "features", "ref", "c", "_ref", "Fragment", "j", "_ref2", "data", "form", "disabled", "onReset", "n", "overrides", "F", "i", "a", "p", "addEventListener", "C", "setForm", "formId", "map", "_ref3", "s", "v", "_objectSpread", "key", "as", "type", "hidden", "readOnly", "name", "_ref4", "document", "getElementById", "closest", "<PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ho<PERSON><PERSON><PERSON><PERSON><PERSON>s"], "sources": ["/Users/<USER>/nocytech/kpss-plus/admin/frontend/node_modules/@headlessui/react/dist/internal/form-fields.js"], "sourcesContent": ["import o,{createContext as H,useContext as E,useEffect as m,useState as u}from\"react\";import{createPortal as g}from\"react-dom\";import{useDisposables as h}from'../hooks/use-disposables.js';import{objectToFormEntries as x}from'../utils/form.js';import{compact as y}from'../utils/render.js';import{Hidden as l,HiddenFeatures as d}from'./hidden.js';let f=H(null);function W(t){let[e,r]=u(null);return o.createElement(f.Provider,{value:{target:e}},t.children,o.createElement(l,{features:d.Hidden,ref:r}))}function c({children:t}){let e=E(f);if(!e)return o.createElement(o.Fragment,null,t);let{target:r}=e;return r?g(o.createElement(o.Fragment,null,t),r):null}function j({data:t,form:e,disabled:r,onReset:n,overrides:F}){let[i,a]=u(null),p=h();return m(()=>{if(n&&i)return p.addEventListener(i,\"reset\",n)},[i,e,n]),o.createElement(c,null,o.createElement(C,{setForm:a,formId:e}),x(t).map(([s,v])=>o.createElement(l,{features:d.Hidden,...y({key:s,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:e,disabled:r,name:s,value:v,...F})})))}function C({setForm:t,formId:e}){return m(()=>{if(e){let r=document.getElementById(e);r&&t(r)}},[t,e]),e?null:o.createElement(l,{features:d.Hidden,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,ref:r=>{if(!r)return;let n=r.closest(\"form\");n&&t(n)}})}export{j as FormFields,W as FormFieldsProvider,c as HoistFormFields};\n"], "mappings": ";AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,WAAW;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,MAAM,IAAIC,CAAC,EAACC,cAAc,IAAIC,CAAC,QAAK,aAAa;AAAC,IAAIC,CAAC,GAACnB,CAAC,CAAC,IAAI,CAAC;AAAC,SAASoB,CAACA,CAACC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACjB,CAAC,CAAC,IAAI,CAAC;EAAC,OAAOR,CAAC,CAAC0B,aAAa,CAACL,CAAC,CAACM,QAAQ,EAAC;IAACC,KAAK,EAAC;MAACC,MAAM,EAACL;IAAC;EAAC,CAAC,EAACD,CAAC,CAACO,QAAQ,EAAC9B,CAAC,CAAC0B,aAAa,CAACR,CAAC,EAAC;IAACa,QAAQ,EAACX,CAAC,CAACH,MAAM;IAACe,GAAG,EAACP;EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAASQ,CAACA,CAAAC,IAAA,EAAc;EAAA,IAAb;IAACJ,QAAQ,EAACP;EAAC,CAAC,GAAAW,IAAA;EAAE,IAAIV,CAAC,GAACpB,CAAC,CAACiB,CAAC,CAAC;EAAC,IAAG,CAACG,CAAC,EAAC,OAAOxB,CAAC,CAAC0B,aAAa,CAAC1B,CAAC,CAACmC,QAAQ,EAAC,IAAI,EAACZ,CAAC,CAAC;EAAC,IAAG;IAACM,MAAM,EAACJ;EAAC,CAAC,GAACD,CAAC;EAAC,OAAOC,CAAC,GAACf,CAAC,CAACV,CAAC,CAAC0B,aAAa,CAAC1B,CAAC,CAACmC,QAAQ,EAAC,IAAI,EAACZ,CAAC,CAAC,EAACE,CAAC,CAAC,GAAC,IAAI;AAAA;AAAC,SAASW,CAACA,CAAAC,KAAA,EAAkD;EAAA,IAAjD;IAACC,IAAI,EAACf,CAAC;IAACgB,IAAI,EAACf,CAAC;IAACgB,QAAQ,EAACf,CAAC;IAACgB,OAAO,EAACC,CAAC;IAACC,SAAS,EAACC;EAAC,CAAC,GAAAP,KAAA;EAAE,IAAG,CAACQ,CAAC,EAACC,CAAC,CAAC,GAACtC,CAAC,CAAC,IAAI,CAAC;IAACuC,CAAC,GAACnC,CAAC,CAAC,CAAC;EAAC,OAAON,CAAC,CAAC,MAAI;IAAC,IAAGoC,CAAC,IAAEG,CAAC,EAAC,OAAOE,CAAC,CAACC,gBAAgB,CAACH,CAAC,EAAC,OAAO,EAACH,CAAC,CAAC;EAAA,CAAC,EAAC,CAACG,CAAC,EAACrB,CAAC,EAACkB,CAAC,CAAC,CAAC,EAAC1C,CAAC,CAAC0B,aAAa,CAACO,CAAC,EAAC,IAAI,EAACjC,CAAC,CAAC0B,aAAa,CAACuB,CAAC,EAAC;IAACC,OAAO,EAACJ,CAAC;IAACK,MAAM,EAAC3B;EAAC,CAAC,CAAC,EAACV,CAAC,CAACS,CAAC,CAAC,CAAC6B,GAAG,CAACC,KAAA;IAAA,IAAC,CAACC,CAAC,EAACC,CAAC,CAAC,GAAAF,KAAA;IAAA,OAAGrD,CAAC,CAAC0B,aAAa,CAACR,CAAC,EAAAsC,aAAA;MAAEzB,QAAQ,EAACX,CAAC,CAACH;IAAM,GAAID,CAAC,CAAAwC,aAAA;MAAEC,GAAG,EAACH,CAAC;MAACI,EAAE,EAAC,OAAO;MAACC,IAAI,EAAC,QAAQ;MAACC,MAAM,EAAC,CAAC,CAAC;MAACC,QAAQ,EAAC,CAAC,CAAC;MAACtB,IAAI,EAACf,CAAC;MAACgB,QAAQ,EAACf,CAAC;MAACqC,IAAI,EAACR,CAAC;MAAC1B,KAAK,EAAC2B;IAAC,GAAIX,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,EAAC,CAAC;AAAA;AAAC,SAASK,CAACA,CAAAc,KAAA,EAAsB;EAAA,IAArB;IAACb,OAAO,EAAC3B,CAAC;IAAC4B,MAAM,EAAC3B;EAAC,CAAC,GAAAuC,KAAA;EAAE,OAAOzD,CAAC,CAAC,MAAI;IAAC,IAAGkB,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACuC,QAAQ,CAACC,cAAc,CAACzC,CAAC,CAAC;MAACC,CAAC,IAAEF,CAAC,CAACE,CAAC,CAAC;IAAA;EAAC,CAAC,EAAC,CAACF,CAAC,EAACC,CAAC,CAAC,CAAC,EAACA,CAAC,GAAC,IAAI,GAACxB,CAAC,CAAC0B,aAAa,CAACR,CAAC,EAAC;IAACa,QAAQ,EAACX,CAAC,CAACH,MAAM;IAACyC,EAAE,EAAC,OAAO;IAACC,IAAI,EAAC,QAAQ;IAACC,MAAM,EAAC,CAAC,CAAC;IAACC,QAAQ,EAAC,CAAC,CAAC;IAAC7B,GAAG,EAACP,CAAC,IAAE;MAAC,IAAG,CAACA,CAAC,EAAC;MAAO,IAAIiB,CAAC,GAACjB,CAAC,CAACyC,OAAO,CAAC,MAAM,CAAC;MAACxB,CAAC,IAAEnB,CAAC,CAACmB,CAAC,CAAC;IAAA;EAAC,CAAC,CAAC;AAAA;AAAC,SAAON,CAAC,IAAI+B,UAAU,EAAC7C,CAAC,IAAI8C,kBAAkB,EAACnC,CAAC,IAAIoC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}