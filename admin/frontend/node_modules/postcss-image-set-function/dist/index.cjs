"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-value-parser"));const n=/^(cross-fade|image|(repeating-)?(conic|linear|radial)-gradient|url|var)$/i;function r(e){return!(!e||!e.type)&&("string"===e.type?"url("+t.default.stringify(e)+")":!("function"!==e.type||!n.test(e.value.toLowerCase()))&&t.default.stringify(e))}const i={dpcm:2.54,dpi:1,dppx:96,x:96};function o(e,t,n){if("boolean"==typeof e)return!1;const r=Math.floor(e/i.x*100)/100;return t.atRule({name:"media",params:`(-webkit-min-device-pixel-ratio: ${r}), (min-resolution: ${e}dpi)`,source:n.source})}function a(e){if(!e)return!1;if("word"!==e.type)return!1;if(!function(e){if(!e||!e.value)return!1;try{return!1!==t.default.unit(e.value)}catch(e){return!1}}(e))return!1;const n=t.default.unit(e.value);return!!n&&(n.unit.toLowerCase()in i&&Number(n.number)*i[n.unit.toLowerCase()])}const s=(e,t,n)=>{if("warn"===e.oninvalid)e.decl.warn(e.result,t,{word:String(n)});else if("throw"===e.oninvalid)throw e.decl.error(t,{word:String(n)})},u=/(^|[^\w-])(-webkit-)?image-set\(/i,l=/^(-webkit-)?image-set$/i,c=e=>{const n=!("preserve"in Object(e))||Boolean(e.preserve),i="oninvalid"in Object(e)?e.oninvalid:"ignore";return{postcssPlugin:"postcss-image-set-function",Declaration(e,{result:c,postcss:f}){const d=e.value;if(!u.test(d.toLowerCase()))return;let p;try{p=t.default(d)}catch(t){e.warn(c,`Failed to parse value '${d}' as an image-set function. Leaving the original value intact.`)}if(void 0===p)return;const v=[];p.walk((n=>{if("function"!==n.type)return;if(!l.test(n.value.toLowerCase()))return;let r=!1;if(t.default.walk(n.nodes,(e=>{"function"===e.type&&l.test(e.value.toLowerCase())&&(r=!0)})),r)return s({decl:e,oninvalid:i,result:c},"nested image-set functions are not allowed",t.default.stringify(n)),!1;const o=n.nodes.filter((e=>"comment"!==e.type&&"space"!==e.type));v.push({imageSetFunction:n,imageSetOptionNodes:o})})),((e,n,i)=>{const u=n.parent,l=new Map,c=n.value;for(let u=0;u<e.length;u++){const{imageSetFunction:d,imageSetOptionNodes:p}=e[u],v=new Map,g=p.length;let m=-1;for(;m<g;){const e=m<0||(f=p[m],"div"===Object(f).type&&","===Object(f).value),u=r(p[m+1]),g=a(p[m+2]),w=o(g,i.postcss,n);if(!e)return void s(i,"expected a comma",t.default.stringify(p));if(!u)return void s(i,"unexpected image",t.default.stringify(p));if(!w||!g||v.has(g))return void s(i,"unexpected resolution",t.default.stringify(p));if(v.set(g,w),l.has(g)){const e=l.get(g);e.value=e.value.replace(t.default.stringify(d),u.trim()),l.set(g,e)}else l.set(g,{atRule:w,value:c.replace(t.default.stringify(d),u.trim())});m+=3}}var f;for(const{atRule:e,value:t}of l.values()){const r=u.clone().removeAll(),i=n.clone({value:t});r.append(i),e.append(r)}const d=Array.from(l.keys()).sort(((e,t)=>e-t)).map((e=>l.get(e).atRule));if(!d.length)return;const p=d[0],v=d.slice(1);v.length&&u.after(v);const g=p.nodes[0].nodes[0];n.cloneBefore({value:g.value.trim()}),i.preserve||(n.remove(),u.nodes.length||u.remove())})(v,e,{decl:e,oninvalid:i,preserve:n,result:c,postcss:f})}}};c.postcss=!0,module.exports=c;
